<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اطلاعات کسب و کار - BitVoucher</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        @font-face {
            font-family: 'IRANSans';
            src: url('irsans.ttf') format('truetype');
            font-weight: normal;
            font-style: normal;
            font-display: swap;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'IRANSans', 'Tahoma', 'Arial', sans-serif;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5rem;
            background: linear-gradient(45deg, #3f51b5, #5a67d8, #667eea);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .header p {
            color: #ffffff;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .form-section {
            background: rgba(255, 255, 255, 0.03);
            border-radius: 15px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.08);
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #ffffff;
            font-weight: bold;
            font-size: 14px;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            color: #ffffff;
            font-family: 'IRANSans', 'Tahoma', 'Arial', sans-serif;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-group input::placeholder,
        .form-group textarea::placeholder {
            color: #a0aec0;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #3f51b5;
            box-shadow: 0 0 0 3px rgba(63, 81, 181, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .submit-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #3f51b5, #5a67d8);
            color: white;
            border: none;
            border-radius: 10px;
            font-family: 'IRANSans', 'Tahoma', 'Arial', sans-serif;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(63, 81, 181, 0.3);
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(63, 81, 181, 0.4);
        }

        .submit-btn:active {
            transform: translateY(0);
        }

        .required {
            color: #f56565;
        }

        .info-box {
            background: rgba(63, 81, 181, 0.1);
            border: 1px solid rgba(63, 81, 181, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .info-box i {
            color: #3f51b5;
            margin-left: 10px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-building" style="color: white;"></i> اطلاعات کسب و کار</h1>
            <p>برای راه‌اندازی سیستم BitVoucher، لطفاً اطلاعات کسب و کار خود را تکمیل کنید</p>
        </div>

        <div class="info-box">
            <i class="fas fa-info-circle"></i>
            <strong>توجه:</strong> تمام اطلاعات وارد شده محرمانه بوده و تنها برای راه‌اندازی سیستم استفاده خواهد شد.
        </div>

        <form id="businessForm" onsubmit="submitForm(event)">
            <div class="form-section">
                <h3 style="color: #3f51b5; margin-bottom: 20px;">
                    <i class="fas fa-user"></i> اطلاعات شخصی
                </h3>

                <div class="form-row">
                    <div class="form-group">
                        <label for="firstName">نام <span class="required">*</span></label>
                        <input type="text" id="firstName" name="firstName" required>
                    </div>
                    <div class="form-group">
                        <label for="lastName">نام خانوادگی <span class="required">*</span></label>
                        <input type="text" id="lastName" name="lastName" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="phone">شماره تماس <span class="required">*</span></label>
                    <input type="tel" id="phone" name="phone" required>
                </div>
            </div>

            <div class="form-section">
                <h3 style="color: white; margin-bottom: 20px;">
                    <i class="fas fa-store" style="color: white;"></i> اطلاعات کسب و کار
                </h3>

                <div class="form-group">
                    <label for="businessName">نام کسب و کار <span class="required">*</span></label>
                    <input type="text" id="businessName" name="businessName" required>
                </div>

                <div class="form-group">
                    <label for="telegramLink">لینک تلگرامی کسب و کار <span class="required">*</span></label>
                    <input type="url" id="telegramLink" name="telegramLink" placeholder="https://t.me/your_business" required>
                </div>

                <div class="form-group">
                    <label for="description">توضیحات <span class="required">*</span></label>
                    <textarea id="description" name="description" placeholder="توضیح مختصری از کسب و کار خود بنویسید..." required></textarea>
                </div>
            </div>

            <button type="submit" class="submit-btn">
                <i class="fas fa-paper-plane"></i>
                ارسال اطلاعات و شروع راه‌اندازی
            </button>
        </form>
    </div>

    <script>
        function submitForm(event) {
            event.preventDefault();
            
            // جمع‌آوری داده‌های فرم
            const formData = new FormData(event.target);
            const data = Object.fromEntries(formData.entries());
            
            // نمایش پیام موفقیت
            alert('اطلاعات شما با موفقیت ارسال شد!\n\nتیم ما در اسرع وقت با شما تماس خواهد گرفت.');
            
            // اینجا می‌توانید داده‌ها را به سرور ارسال کنید
            console.log('Business Info:', data);
            
            // بستن پنجره یا هدایت به صفحه تشکر
            // window.close();
        }

        // اعتبارسنجی شماره تلفن
        document.getElementById('phone').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 11) {
                value = value.slice(0, 11);
            }
            e.target.value = value;
        });
    </script>
</body>
</html>
